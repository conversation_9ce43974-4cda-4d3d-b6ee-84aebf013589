import React, { useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Link
} from '@mui/material';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  // Ensure Termly initializes and (re)binds the preference button in SPA context
  useEffect(() => {
    // If the embed exposes a global Termly object, initialize to bind UI hooks
    if (typeof window !== 'undefined' && (window as any).Termly?.initialize) {
      try {
        (window as any).Termly.initialize();
      } catch (e) {
        // no-op
      }
    }
  }, []);

  return (
    <Box
      component="footer"
      sx={{
        mt: 'auto',
        py: 3,
        backgroundColor: 'grey.50',
        borderTop: 1,
        borderColor: 'divider'
      }}
    >
      <Container maxWidth="lg">
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            justifyContent: 'space-between',
            alignItems: { xs: 'center', sm: 'flex-start' },
            gap: 2
          }}
        >
          {/* Left side - Copyright */}
          <Box sx={{ textAlign: { xs: 'center', sm: 'left' } }}>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ mb: 1 }}
            >
              © {currentYear} Gallery Tuner. All rights reserved.
            </Typography>
          </Box>

          {/* Right side - Links */}
          <Box
            sx={{
              display: 'flex',
              flexDirection: { xs: 'column', sm: 'row' },
              alignItems: 'center',
              gap: { xs: 1, sm: 3 },
              textAlign: 'center'
            }}
          >
          <a
            href="#"
            className="termly-display-preferences"
            style={{
              color: 'inherit',
              textDecoration: 'none',
              fontSize: '0.875rem',
              cursor: 'pointer',
              border: 'none',
              background: 'none',
              padding: 0,
              fontFamily: 'inherit'
            }}
          >
            Consent Preferences
          </a>
            
            <Link
              href="/privacy-policy"
              sx={{
                color: 'text.secondary',
                textDecoration: 'none',
                fontSize: '0.875rem',
                '&:hover': {
                  color: 'primary.main',
                  textDecoration: 'underline'
                }
              }}
            >
              Privacy Policy
            </Link>
            
            <Link
              href="/terms-of-service"
              sx={{
                color: 'text.secondary',
                textDecoration: 'none',
                fontSize: '0.875rem',
                '&:hover': {
                  color: 'primary.main',
                  textDecoration: 'underline'
                }
              }}
            >
              Terms of Service
            </Link>

            <Link
              href="/cookie-policy"
              sx={{
                color: 'text.secondary',
                textDecoration: 'none',
                fontSize: '0.875rem',
                '&:hover': {
                  color: 'primary.main',
                  textDecoration: 'underline'
                }
              }}
            >
              Cookie Policy
            </Link>
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

export default Footer;
